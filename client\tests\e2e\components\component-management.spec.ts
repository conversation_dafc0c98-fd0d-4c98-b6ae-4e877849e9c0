/**
 * Component Management E2E Tests
 * Tests complete component management workflows in a real browser environment
 */

import { expect, Page, test } from '@playwright/test';
import { loginUser, overrideApiHandler, resetMocks } from '../msw-utils';

// Test data
const testComponent = {
  name: 'Test E2E Resistor',
  manufacturer: 'E2E Electronics',
  part_number: 'E2E-R-001',
  model_number: 'E2ER001',
  category: 'RESISTOR',
  component_type: 'RESISTOR',
  description: 'High precision resistor for E2E testing',
  price: '0.25',
  currency: 'EUR',
}

const updatedComponent = {
  name: 'Updated E2E Resistor',
  description: 'Updated description for E2E testing',
  price: '0.30',
}

// Page Object Model for Component Management
class ComponentManagementPage {
  constructor(private page: Page) {}

  // Navigation
  async goto() {
    await this.page.goto('/components')
    await this.page.waitForLoadState('networkidle')
  }

  // Component List
  getComponentList() {
    return this.page.locator('[data-testid="component-list"]')
  }

  getComponentCard(id: number) {
    return this.page.locator(`[data-testid="component-card-${id}"]`)
  }

  getComponentByName(name: string) {
    return this.page.locator(`[data-testid="component-card"]`).filter({ hasText: name })
  }

  // Search and Filters
  async searchComponents(query: string) {
    await this.page.fill('[data-testid="search-input"]', query)
    await this.page.keyboard.press('Enter')
    await this.page.waitForLoadState('networkidle')
  }

  async filterByCategory(category: string) {
    await this.page.selectOption('[data-testid="category-filter"]', category)
    await this.page.waitForLoadState('networkidle')
  }

  async filterByManufacturer(manufacturer: string) {
    await this.page.fill('[data-testid="manufacturer-filter"]', manufacturer)
    await this.page.waitForLoadState('networkidle')
  }

  async clearFilters() {
    await this.page.click('[data-testid="clear-filters-btn"]')
    await this.page.waitForLoadState('networkidle')
  }

  // Component Form
  async openCreateForm() {
    await this.page.click('[data-testid="add-component-btn"]')
    await this.page.waitForSelector('[data-testid="component-form"]')
  }

  async openEditForm(componentId: number) {
    await this.page.click(`[data-testid="edit-component-${componentId}"]`)
    await this.page.waitForSelector('[data-testid="component-form"]')
  }

  async fillComponentForm(component: Partial<typeof testComponent>) {
    if (component.name) {
      await this.page.fill('[data-testid="form-name"]', component.name)
    }
    if (component.manufacturer) {
      await this.page.fill('[data-testid="form-manufacturer"]', component.manufacturer)
    }
    if (component.part_number) {
      await this.page.fill('[data-testid="form-part-number"]', component.part_number)
    }
    if (component.model_number) {
      await this.page.fill('[data-testid="form-model-number"]', component.model_number)
    }
    if (component.category) {
      await this.page.selectOption('[data-testid="form-category"]', component.category)
    }
    if (component.component_type) {
      await this.page.selectOption('[data-testid="form-component-type"]', component.component_type)
    }
    if (component.description) {
      await this.page.fill('[data-testid="form-description"]', component.description)
    }
    if (component.price) {
      await this.page.fill('[data-testid="form-price"]', component.price)
    }
  }

  async submitForm() {
    await this.page.click('[data-testid="form-submit"]')
    await this.page.waitForLoadState('networkidle')
  }

  async cancelForm() {
    await this.page.click('[data-testid="form-cancel"]')
  }

  // Component Details
  async openComponentDetails(componentId: number) {
    await this.page.click(`[data-testid="view-component-${componentId}"]`)
    await this.page.waitForSelector('[data-testid="component-details"]')
  }

  async closeComponentDetails() {
    await this.page.click('[data-testid="close-details"]')
  }

  // Bulk Operations
  async selectComponent(componentId: number) {
    await this.page.check(`[data-testid="select-component-${componentId}"]`)
  }

  async selectAllComponents() {
    await this.page.check('[data-testid="select-all-components"]')
  }

  async openBulkOperations() {
    await this.page.click('[data-testid="bulk-operations-btn"]')
    await this.page.waitForSelector('[data-testid="bulk-operations-panel"]')
  }

  async performBulkDelete() {
    await this.page.click('[data-testid="bulk-delete-btn"]')
    await this.page.click('[data-testid="confirm-bulk-delete"]')
    await this.page.waitForLoadState('networkidle')
  }

  // View Mode
  async switchToGridView() {
    await this.page.click('[data-testid="grid-view-btn"]')
  }

  async switchToListView() {
    await this.page.click('[data-testid="list-view-btn"]')
  }

  async switchToTableView() {
    await this.page.click('[data-testid="table-view-btn"]')
  }

  // Pagination
  async goToNextPage() {
    await this.page.click('[data-testid="next-page-btn"]')
    await this.page.waitForLoadState('networkidle')
  }

  async goToPreviousPage() {
    await this.page.click('[data-testid="prev-page-btn"]')
    await this.page.waitForLoadState('networkidle')
  }

  async changePageSize(size: string) {
    await this.page.selectOption('[data-testid="page-size-select"]', size)
    await this.page.waitForLoadState('networkidle')
  }
}

test.describe('Component Management E2E', () => {
  let componentPage: ComponentManagementPage

  test.beforeEach(async ({ page }) => {
    componentPage = new ComponentManagementPage(page);
    resetMocks();

    // Set up API route interception for component endpoints
    await page.route('**/api/v1/components/**', async (route) => {
      const url = new URL(route.request().url())
      const method = route.request().method()

      console.log('🔍 Route intercepted:', method, url.pathname, url.search)

      // Handle GET /api/v1/components/ (list components)
      if (method === 'GET' && url.pathname.endsWith('/api/v1/components/')) {
        console.log('✅ Returning mock component list')
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            items: [
              {
                id: 1,
                name: 'Test E2E Resistor',
                manufacturer: 'E2E Electronics',
                model_number: 'E2ER001',
                part_number: 'E2E-R-001',
                description: 'High precision resistor for E2E testing',
                unit_price: 0.25,
                currency: 'EUR',
                weight_kg: 0.001,
                component_category_id: 1,
                component_type_id: 1,
                category: 'RESISTOR',
                component_type: 'RESISTOR',
                supplier: 'E2E Supplier',
                is_active: true,
                is_preferred: false,
                created_at: '2024-01-15T10:00:00Z',
                updated_at: '2024-01-15T10:00:00Z',
              },
              {
                id: 2,
                name: 'Premium Capacitor',
                manufacturer: 'Schneider Electric',
                model_number: 'SC-CAP-100',
                part_number: 'SC-C-100',
                description: 'High-quality ceramic capacitor',
                unit_price: 1.50,
                currency: 'EUR',
                weight_kg: 0.002,
                component_category_id: 2,
                component_type_id: 2,
                category: 'CAPACITOR',
                component_type: 'CAPACITOR',
                supplier: 'Schneider Supply',
                is_active: true,
                is_preferred: true,
                created_at: '2024-01-15T10:00:00Z',
                updated_at: '2024-01-15T10:00:00Z',
              }
            ],
            total: 2,
            page: 1,
            size: 10,
            pages: 1
          })
        })
        return
      }

      // Handle GET /api/v1/components/categories
      if (method === 'GET' && url.pathname.endsWith('/api/v1/components/categories')) {
        console.log('✅ Returning mock component categories')
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            { name: 'Resistor', value: 'RESISTOR' },
            { name: 'Capacitor', value: 'CAPACITOR' },
            { name: 'Inductor', value: 'INDUCTOR' }
          ])
        })
        return
      }

      // Handle GET /api/v1/components/types
      if (method === 'GET' && url.pathname.endsWith('/api/v1/components/types')) {
        console.log('✅ Returning mock component types')
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            { name: 'Fixed Resistor', value: 'FIXED_RESISTOR' },
            { name: 'Variable Resistor', value: 'VARIABLE_RESISTOR' },
            { name: 'Ceramic Capacitor', value: 'CERAMIC_CAPACITOR' },
            { name: 'Electrolytic Capacitor', value: 'ELECTROLYTIC_CAPACITOR' }
          ])
        })
        return
      }

      // Handle other component endpoints
      console.log('⚠️ Unhandled component route:', method, url.pathname)
      await route.continue()
    })

    // Navigate to the page first to establish domain context
    await componentPage.goto();
    // Then set up authentication
    await loginUser(page);
    // Reload the page to apply authentication
    await page.reload();
  });

  test.describe('Component List and Navigation', () => {
    test('should display component list', async ({ page }) => {
      // Listen for console errors
      page.on('console', msg => {
        if (msg.type() === 'error') {
          console.log('❌ Browser console error:', msg.text())
        }
      })

      // Listen for page errors
      page.on('pageerror', error => {
        console.log('❌ Page error:', error.message)
      })

      // Debug: Check what's actually on the page
      console.log('🔍 Page URL:', page.url())
      console.log('🔍 Page title:', await page.title())

      // Wait a bit for the page to load
      await page.waitForTimeout(2000)

      // Check the page content
      const bodyText = await page.locator('body').textContent()
      console.log('🔍 Page body text (first 200 chars):', bodyText?.substring(0, 200))

      // Check if there are any error messages
      const errorMessages = await page.locator('[role="alert"], .error, .alert-error').allTextContents()
      if (errorMessages.length > 0) {
        console.log('❌ Error messages found:', errorMessages)
      }

      // Check if loading indicators are present
      const loadingIndicators = await page.locator('[data-testid*="loading"], .loading, .spinner').count()
      console.log('⏳ Loading indicators count:', loadingIndicators)

      // Check what elements are actually present
      const allTestIds = await page.locator('[data-testid]').allTextContents()
      console.log('🔍 All elements with data-testid:', allTestIds)

      // Take a screenshot for debugging
      await page.screenshot({ path: 'debug-component-list.png', fullPage: true })

      await expect(componentPage.getComponentList()).toBeVisible()

      // Should show components
      const componentCards = page.locator('[data-testid^="component-card-"]')
      await expect(componentCards.first()).toBeVisible()
    })

    test('should switch between view modes', async ({ page }) => {
      // Test grid view
      await componentPage.switchToGridView()
      await expect(page.locator('[data-testid="grid-view-btn"]')).toHaveClass(/active/)

      // Test list view
      await componentPage.switchToListView()
      await expect(page.locator('[data-testid="list-view-btn"]')).toHaveClass(/active/)

      // Test table view
      await componentPage.switchToTableView()
      await expect(page.locator('[data-testid="table-view-btn"]')).toHaveClass(/active/)
    })

    test('should handle pagination', async ({ page }) => {
      // Change page size
      await componentPage.changePageSize('5')
      await expect(page.locator('[data-testid="page-size-select"]')).toHaveValue('5')

      // Navigate pages (if multiple pages exist)
      const nextButton = page.locator('[data-testid="next-page-btn"]')
      if (await nextButton.isEnabled()) {
        await componentPage.goToNextPage()
        await expect(page.locator('[data-testid="current-page"]')).toContainText('2')

        await componentPage.goToPreviousPage()
        await expect(page.locator('[data-testid="current-page"]')).toContainText('1')
      }
    })
  })

  test.describe('Component Search and Filtering', () => {
    test('should search components by name', async ({ page }) => {
      await componentPage.searchComponents('resistor')

      // Should show search results
      await expect(page.locator('[data-testid="search-input"]')).toHaveValue('resistor')

      // Results should contain search term
      const results = page.locator('[data-testid^="component-card-"]')
      await expect(results.first()).toContainText(/resistor/i)
    })

    test('should filter components by category', async ({ page }) => {
      await componentPage.filterByCategory('RESISTOR')

      await expect(page.locator('[data-testid="category-filter"]')).toHaveValue('RESISTOR')

      // All visible components should be resistors
      const componentCards = page.locator('[data-testid^="component-card-"]')
      const count = await componentCards.count()

      for (let i = 0; i < count; i++) {
        await expect(componentCards.nth(i)).toContainText(/resistor/i)
      }
    })

    test('should combine multiple filters', async ({ page }) => {
      await componentPage.searchComponents('test')
      await componentPage.filterByCategory('RESISTOR')
      await componentPage.filterByManufacturer('Test Electronics')

      // All filters should be applied
      await expect(page.locator('[data-testid="search-input"]')).toHaveValue('test')
      await expect(page.locator('[data-testid="category-filter"]')).toHaveValue('RESISTOR')
      await expect(page.locator('[data-testid="manufacturer-filter"]')).toHaveValue(
        'Test Electronics'
      )
    })

    test('should clear all filters', async ({ page }) => {
      // Apply filters
      await componentPage.searchComponents('test')
      await componentPage.filterByCategory('RESISTOR')

      // Clear filters
      await componentPage.clearFilters()

      // All filters should be cleared
      await expect(page.locator('[data-testid="search-input"]')).toHaveValue('')
      await expect(page.locator('[data-testid="category-filter"]')).toHaveValue('')
    })
  })

  test.describe('Component Creation Workflow', () => {
    test('should create a new component successfully', async ({ page }) => {
      await componentPage.openCreateForm()

      // Form should be visible
      await expect(page.locator('[data-testid="component-form"]')).toBeVisible()
      await expect(page.locator('[data-testid="form-title"]')).toContainText('Add Component')

      // Fill form
      await componentPage.fillComponentForm(testComponent)

      // Submit form
      await componentPage.submitForm()

      // Should show success message
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible()

      // Component should appear in list
      await expect(componentPage.getComponentByName(testComponent.name)).toBeVisible()
    })

    test('should validate required fields', async ({ page }) => {
      await componentPage.openCreateForm()

      // Try to submit empty form
      await componentPage.submitForm()

      // Should show validation errors
      await expect(page.locator('[data-testid="error-name"]')).toBeVisible()
      await expect(page.locator('[data-testid="error-manufacturer"]')).toBeVisible()
      await expect(page.locator('[data-testid="error-part-number"]')).toBeVisible()
    })

    test('should cancel form creation', async ({ page }) => {
      await componentPage.openCreateForm()

      // Fill partial data
      await componentPage.fillComponentForm({ name: 'Partial Component' })

      // Cancel form
      await componentPage.cancelForm()

      // Form should be closed
      await expect(page.locator('[data-testid="component-form"]')).not.toBeVisible()

      // Component should not be created
      await expect(componentPage.getComponentByName('Partial Component')).not.toBeVisible()
    })
  })

  test.describe('Component Editing Workflow', () => {
    test('should edit an existing component', async ({ page }) => {
      // First, ensure we have a component to edit
      const firstComponent = page.locator('[data-testid^="component-card-"]').first()
      await expect(firstComponent).toBeVisible()

      // Get component ID from the first component
      const componentId = await firstComponent.getAttribute('data-testid')
      const id = componentId?.split('-')[2]

      if (id) {
        await componentPage.openEditForm(parseInt(id))

        // Form should be pre-populated
        await expect(page.locator('[data-testid="component-form"]')).toBeVisible()
        await expect(page.locator('[data-testid="form-title"]')).toContainText('Edit Component')

        // Update component
        await componentPage.fillComponentForm(updatedComponent)
        await componentPage.submitForm()

        // Should show success message
        await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
      }
    })

    test('should preserve unchanged fields during edit', async ({ page }) => {
      const firstComponent = page.locator('[data-testid^="component-card-"]').first()
      const originalName = await firstComponent
        .locator('[data-testid="component-name"]')
        .textContent()

      const componentId = await firstComponent.getAttribute('data-testid')
      const id = componentId?.split('-')[2]

      if (id && originalName) {
        await componentPage.openEditForm(parseInt(id))

        // Only update description
        await componentPage.fillComponentForm({ description: 'Updated description only' })
        await componentPage.submitForm()

        // Name should remain unchanged
        await expect(firstComponent.locator('[data-testid="component-name"]')).toContainText(
          originalName
        )
      }
    })
  })

  test.describe('Component Details View', () => {
    test('should display component details', async ({ page }) => {
      const firstComponent = page.locator('[data-testid^="component-card-"]').first()
      const componentId = await firstComponent.getAttribute('data-testid')
      const id = componentId?.split('-')[2]

      if (id) {
        await componentPage.openComponentDetails(parseInt(id))

        // Details panel should be visible
        await expect(page.locator('[data-testid="component-details"]')).toBeVisible()

        // Should show component information
        await expect(page.locator('[data-testid="details-name"]')).toBeVisible()
        await expect(page.locator('[data-testid="details-manufacturer"]')).toBeVisible()
        await expect(page.locator('[data-testid="details-part-number"]')).toBeVisible()

        // Close details
        await componentPage.closeComponentDetails()
        await expect(page.locator('[data-testid="component-details"]')).not.toBeVisible()
      }
    })

    test('should navigate between components in details view', async ({ page }) => {
      const firstComponent = page.locator('[data-testid^="component-card-"]').first()
      const componentId = await firstComponent.getAttribute('data-testid')
      const id = componentId?.split('-')[2]

      if (id) {
        await componentPage.openComponentDetails(parseInt(id))

        // Should have navigation buttons
        const nextButton = page.locator('[data-testid="next-component-btn"]')
        const prevButton = page.locator('[data-testid="prev-component-btn"]')

        if (await nextButton.isVisible()) {
          await nextButton.click()
          // Should show next component details
          await expect(page.locator('[data-testid="component-details"]')).toBeVisible()
        }
      }
    })
  })

  test.describe('Bulk Operations', () => {
    test('should select multiple components', async ({ page }) => {
      // Select first two components
      const components = page.locator('[data-testid^="component-card-"]')
      const count = Math.min(await components.count(), 2)

      for (let i = 0; i < count; i++) {
        const componentId = await components.nth(i).getAttribute('data-testid')
        const id = componentId?.split('-')[2]
        if (id) {
          await componentPage.selectComponent(parseInt(id))
        }
      }

      // Should show selection count
      await expect(page.locator('[data-testid="selection-count"]')).toContainText(
        `${count} selected`
      )
    })

    test('should select all components', async ({ page }) => {
      await componentPage.selectAllComponents()

      // All checkboxes should be checked
      const checkboxes = page.locator('[data-testid^="select-component-"]')
      const count = await checkboxes.count()

      for (let i = 0; i < count; i++) {
        await expect(checkboxes.nth(i)).toBeChecked()
      }
    })

    test('should perform bulk operations', async ({ page }) => {
      // Select components
      await componentPage.selectAllComponents()

      // Open bulk operations
      await componentPage.openBulkOperations()

      // Should show bulk operations panel
      await expect(page.locator('[data-testid="bulk-operations-panel"]')).toBeVisible()

      // Should have bulk operation buttons
      await expect(page.locator('[data-testid="bulk-edit-btn"]')).toBeVisible()
      await expect(page.locator('[data-testid="bulk-delete-btn"]')).toBeVisible()
    })
  })

  test.describe('Responsive Design', () => {
    test('should work on mobile viewport', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })

      // Component list should be responsive
      await expect(componentPage.getComponentList()).toBeVisible()

      // Mobile menu should be accessible
      const mobileMenu = page.locator('[data-testid="mobile-menu-btn"]')
      if (await mobileMenu.isVisible()) {
        await mobileMenu.click()
        await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible()
      }
    })

    test('should work on tablet viewport', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 })

      // Should adapt layout for tablet
      await expect(componentPage.getComponentList()).toBeVisible()

      // Grid should show appropriate number of columns
      const gridContainer = page.locator('[data-testid="component-grid"]')
      if (await gridContainer.isVisible()) {
        await expect(gridContainer).toHaveClass(/md:grid-cols-2/)
      }
    })
  })

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      overrideApiHandler('/api/v1/components', 'get', { error: 'Internal Server Error' }, 500);
      await componentPage.goto();

      // Should show error message
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();

      // Should have retry button
      await expect(page.locator('[data-testid="retry-btn"]')).toBeVisible();
    });

    test('should recover from errors', async ({ page }) => {
      // First request fails
      overrideApiHandler('/api/v1/components', 'get', { error: 'Internal Server Error' }, 500);
      await componentPage.goto();

      // Should show error initially
      await expect(page.locator('[data-testid="error-message"]')).toBeVisible();

      // Reset the handler to a successful response
      resetMocks();
      
      // Click retry
      await page.click('[data-testid="retry-btn"]');

      // Should recover and show components
      await expect(componentPage.getComponentList()).toBeVisible();
    });
  });

  test.describe('Accessibility', () => {
    test('should be keyboard navigable', async ({ page }) => {
      // Tab through interface
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')

      // Should be able to activate buttons with Enter
      const focusedElement = page.locator(':focus')
      await expect(focusedElement).toBeVisible()

      await page.keyboard.press('Enter')
    })

    test('should have proper ARIA labels', async ({ page }) => {
      // Check for ARIA labels on key elements
      await expect(page.locator('[data-testid="search-input"]')).toHaveAttribute('aria-label')
      await expect(page.locator('[data-testid="component-list"]')).toHaveAttribute('role', 'region')
    })

    test('should support screen readers', async ({ page }) => {
      // Check for proper heading structure
      await expect(page.locator('h1')).toBeVisible()

      // Check for descriptive text
      const componentCards = page.locator('[data-testid^="component-card-"]')
      await expect(componentCards.first()).toHaveAttribute('aria-label')
    })
  })
})
